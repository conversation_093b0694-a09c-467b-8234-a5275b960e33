<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.dxy.data</groupId>
  <artifactId>pi-sdk-parent_2.13</artifactId>
    <version>4.6.7-jdk17-SNAPSHOT</version>
  <packaging>pom</packaging>

  <modules>
    <module>pi-meta-client</module>
    <module>pi-sdk-guava</module>
    <module>pi-sdk-hbase</module>
    <module>pi-sdk-core</module>
<!--    <module>pi-exporter</module>-->
<!--    <module>pi-sdk-service</module>-->
    <module>pi-service-api</module>
    <module>pi-sdk-spark</module>
    <module>pi-assembly</module>
  </modules>

  <properties>
    <java-version>17</java-version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <scala.binary.version>2.13</scala.binary.version>
    <scala.version>2.13.16</scala.version>
    <hbase.version>2.5.8-hadoop3</hbase.version>
    <kafka.version>2.0.0</kafka.version>
    <build.lib.path>${project.basedir}/libs</build.lib.path>
    <skipTests>false</skipTests>
    <scope.type>compile</scope.type>
    <swagger.version>2.8.0</swagger.version>
    <spark.version>3.3.4</spark.version>
    <spark.scope>provided</spark.scope>
    <jackson.version>2.13.4</jackson.version>
    <meta.client>1.0-SNAPSHOT</meta.client>
    <zookeeper.version>3.6.3</zookeeper.version>
    <mode>pro</mode>
    <elastic4s.jackson.version>2.13.4</elastic4s.jackson.version>
    <elastic4s.version>5.4.6</elastic4s.version>
    <log4j.version>2.17.1</log4j.version>
    <sdk.guava.version>20.0</sdk.guava.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.spark</groupId>
        <artifactId>spark-core_${scala.binary.version}</artifactId>
        <version>${spark.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.spark</groupId>
        <artifactId>spark-sql-kafka-0-10_${scala.binary.version}</artifactId>
        <version>${spark.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.spark</groupId>
        <artifactId>spark-sql_${scala.binary.version}</artifactId>
        <version>${spark.version}</version>
      </dependency>

      <dependency>
        <groupId>com.dxy.data</groupId>
        <artifactId>module-config</artifactId>
        <version>1.5-SNAPSHOT</version>
      </dependency>

      <dependency>
        <groupId>com.dxy.data</groupId>
        <artifactId>module-http</artifactId>
        <version>2.0-SNAPSHOT</version>
        <exclusions>
          <exclusion>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>joda-time</groupId>
        <artifactId>joda-time</artifactId>
        <version>2.10</version>
      </dependency>

      <dependency>
        <artifactId>jackson-databind</artifactId>
        <groupId>com.fasterxml.jackson.core</groupId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.13</artifactId>
        <version>${jackson.version}</version>
      </dependency>


      <dependency>
        <groupId>org.msgpack</groupId>
        <artifactId>jackson-dataformat-msgpack</artifactId>
        <version>0.8.16</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.8</version>
      </dependency>

      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api-scala_${scala.binary.version}</artifactId>
        <version>12.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${log4j.version}</version>
      </dependency>


      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-client</artifactId>
        <version>${hbase.version}</version>
        <exclusions>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
          <exclusion>
            <artifactId>jackson-mapper-asl</artifactId>
            <groupId>org.codehaus.jackson</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jackson-core-asl</artifactId>
            <groupId>org.codehaus.jackson</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-server</artifactId>
        <version>${hbase.version}</version>
        <exclusions>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
          <exclusion>
            <artifactId>jackson-mapper-asl</artifactId>
            <groupId>org.codehaus.jackson</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jackson-core-asl</artifactId>
            <groupId>org.codehaus.jackson</groupId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-common</artifactId>
        <version>${hbase.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>jackson-mapper-asl</artifactId>
            <groupId>org.codehaus.jackson</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jackson-core-asl</artifactId>
            <groupId>org.codehaus.jackson</groupId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>tomcat</groupId>
            <artifactId>jasper-compiler</artifactId>
          </exclusion>
          <exclusion>
            <groupId>tomcat</groupId>
            <artifactId>jasper-runtime</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase-mapreduce</artifactId>
        <version>${hbase.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.hbase</groupId>
        <artifactId>hbase</artifactId>
        <version>${hbase.version}</version>
        <type>pom</type>
      </dependency>

        <dependency>
            <groupId>com.sksamuel.elastic4s</groupId>
            <artifactId>elastic4s-http_${scala.binary.version}</artifactId>
            <version>${elastic4s.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api-scala_${scala.binary.version}</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
      <dependency>
        <groupId>org.scala-lang</groupId>
        <artifactId>scala-reflect</artifactId>
        <version>${scala.version}</version>
      </dependency>
      <dependency>
        <groupId>com.typesafe.akka</groupId>
        <artifactId>akka-actor_${scala.binary.version}</artifactId>
        <version>2.5.32</version>
      </dependency>
      <dependency>
        <groupId>io.lettuce</groupId>
        <artifactId>lettuce-core</artifactId>
        <version>5.1.3.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.dxy.data</groupId>
        <artifactId>meta-client</artifactId>
        <version>${meta.client}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>${zookeeper.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.scala-lang</groupId>
      <artifactId>scala-library</artifactId>
      <version>${scala.version}</version>
    </dependency>
    <dependency>
      <groupId>org.scalatest</groupId>
      <artifactId>scalatest_${scala.binary.version}</artifactId>
      <version>3.0.8</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <directory>src/main/resources-${mode}</directory>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.0.0</version>
        <configuration>
          <failOnViolation>true</failOnViolation>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <sourceDirectories>${basedir}/src/main/java,${basedir}/src/main/scala</sourceDirectories>
          <testSourceDirectories>${basedir}/src/test/java</testSourceDirectories>
          <configLocation>checkstyle.xml</configLocation>
          <outputFile>${basedir}/target/checkstyle-output.xml</outputFile>
          <!--<inputEncoding>${project.build.sourceEncoding}</inputEncoding>-->
          <!--<outputEncoding>${project.reporting.outputEncoding}</outputEncoding>-->
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>8.11</version>
          </dependency>
        </dependencies>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <source>${java-version}</source>
          <target>${java-version}</target>
          <encoding>UTF-8</encoding>
          <excludes>
            <exclude>org/apache/hadoop/classification/tools/</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.scalastyle</groupId>
        <artifactId>scalastyle-maven-plugin</artifactId>
        <version>0.8.0</version>
        <configuration>
          <verbose>false</verbose>
          <failOnViolation>true</failOnViolation>
          <includeTestSourceDirectory>false</includeTestSourceDirectory>
          <failOnWarning>false</failOnWarning>
          <sourceDirectory>${project.basedir}/src/main/scala</sourceDirectory>
          <testSourceDirectory>${basedir}/src/test/scala</testSourceDirectory>
          <configLocation>scalastyle-config.xml</configLocation>
          <outputFile>${basedir}/target/scalastyle-output.xml</outputFile>
          <inputEncoding>${project.build.sourceEncoding}</inputEncoding>
          <outputEncoding>${project.reporting.outputEncoding}</outputEncoding>
        </configuration>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.scala-tools</groupId>
        <artifactId>maven-scala-plugin</artifactId>
        <version>2.15.2</version>
        <configuration>
          <args>
            <arg>
              -g:vars
            </arg>
          </args>
          <verbose>true</verbose>
        </configuration>
        <executions>
          <execution>
            <id>scala-compile-first</id>
            <phase>process-resources</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
          <execution>
            <id>scala-test-compile-first</id>
            <goals>
              <goal>testCompile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.scalatest</groupId>
        <artifactId>scalatest-maven-plugin</artifactId>
        <version>1.0</version>
        <!-- Note config is repeated in surefire config -->
        <configuration>
          <skipTests>${skipTests}</skipTests>
          <testFailureIgnore>false</testFailureIgnore>
          <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
          <junitxml>.</junitxml>
          <argLine>-ea -Xmx3g</argLine>
          <filereports>WDF TestSuite.txt</filereports>
          <stderr/>
          <systemProperties>
            <log4j.configuration>file:src/main/resources/log4j.properties</log4j.configuration>
            <log4j.configurationFile>file:src/main/resources/log4j2.xml</log4j.configurationFile>
          </systemProperties>
        </configuration>
        <executions>
          <execution>
            <id>test</id>
            <goals>
              <goal>test</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.0.1</version>
        <configuration>
          <attach>true</attach>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
        <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>versions-maven-plugin</artifactId>
            <version>2.7</version>
            <configuration>
                <generateBackupPoms>false</generateBackupPoms>
            </configuration>
        </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>pro</id>
        <activation>
          <activeByDefault>true</activeByDefault>
        </activation>
      <properties>
        <spark.scope>provided</spark.scope>
        <sdk.guava.version>20.0</sdk.guava.version>
        <mode>pro</mode>
      </properties>
    </profile>
    <profile>
      <id>dev</id>
      <properties>
        <spark.scope>compile</spark.scope>
        <sdk.guava.version>12.0.1</sdk.guava.version>
        <mode>dev</mode>
      </properties>
    </profile>
      <profile>
          <id>scala-2.11</id>
          <properties>
              <elastic4s.jackson.version>2.9.9</elastic4s.jackson.version>
              <elastic4s.version>5.4.6</elastic4s.version>
          </properties>
          <dependencies>

          </dependencies>
      </profile>

      <profile>
          <id>scala-2.12</id>
        <activation>
          <activeByDefault>true</activeByDefault>
        </activation>
          <properties>
              <elastic4s.jackson.version>2.13.4</elastic4s.jackson.version>
              <elastic4s.version>5.6.11</elastic4s.version>
          </properties>
      </profile>
  </profiles>

  <distributionManagement>
    <repository>
      <id>nexus-releases</id>
      <name>Nexus Release Repository</name>
      <url>https://nexus.k8s.qc.host.dxy/repository/maven-releases/</url>
    </repository>
    <snapshotRepository>
      <id>nexus-snapshots</id>
      <name>Nexus Snapshot Repository</name>
      <url>https://nexus.k8s.qc.host.dxy/repository/maven-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

  <repositories>
    <repository>
      <id>nexus</id>
      <name>dxy private repository</name>
      <url>https://nexus.k8s.qc.host.dxy/repository/maven-public/</url>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
        <checksumPolicy>fail</checksumPolicy>
      </snapshots>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
        <checksumPolicy>fail</checksumPolicy>
      </releases>
      <layout>default</layout>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>aliyun</id>
      <name>Nexus Release Repository</name>
      <url>http://maven.aliyun.com/nexus/content/groups/public</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </pluginRepository>
  </pluginRepositories>
</project>
